{"info": {"name": "09-<PERSON><PERSON>", "description": "Redis operations and testing endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/redis/ping", "host": ["{{base_url}}"], "path": ["api", "redis", "ping"]}, "description": "Ping Redis server to check connectivity"}}, {"name": "Redis Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/redis/status", "host": ["{{base_url}}"], "path": ["api", "redis", "status"]}, "description": "Get Redis server status and information"}}, {"name": "List Redis Keys", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/redis/keys", "host": ["{{base_url}}"], "path": ["api", "redis", "keys"]}, "description": "List all Redis keys"}}, {"name": "Get Redis Key", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/redis/{{redis_key}}", "host": ["{{base_url}}"], "path": ["api", "redis", "{{redis_key}}"]}, "description": "Get value by Redis key"}}, {"name": "Set Redis Key", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"key\": \"test_key\",\n  \"value\": \"test_value\",\n  \"ttl\": 3600\n}"}, "url": {"raw": "{{base_url}}/api/redis", "host": ["{{base_url}}"], "path": ["api", "redis"]}, "description": "Set a key-value pair in Redis with optional TTL"}}, {"name": "Delete Redis Key", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/redis/{{redis_key}}", "host": ["{{base_url}}"], "path": ["api", "redis", "{{redis_key}}"]}, "description": "Delete a Redis key"}}]}