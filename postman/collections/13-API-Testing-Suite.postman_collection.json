{"info": {"name": "13-API-Testing-Suite", "description": "Comprehensive API testing suite for Platform Rust API - includes all endpoints for testing", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "item": [{"name": "🏥 Health Checks", "item": [{"name": "Simple Health Check", "request": {"method": "GET", "header": [], "url": "{{base_url}}/health"}}, {"name": "Detailed Health Check", "request": {"method": "GET", "header": [], "url": "{{base_url}}/health/detailed"}}]}, {"name": "🔐 Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{test_email}}\",\n    \"password\": \"{{test_password}}\"\n}"}, "url": "{{base_url}}/api/auth/login"}}, {"name": "<PERSON><PERSON><PERSON> - Google", "request": {"method": "GET", "header": [], "url": "{{base_url}}/api/auth/oauth/google"}}]}, {"name": "👤 Profile", "item": [{"name": "Get My Profile", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": "{{base_url}}/api/profile/me"}}]}, {"name": "💻 Laptops (Public)", "item": [{"name": "Get Laptops", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops?page=1&per_page=10", "host": ["{{base_url}}"], "path": ["api", "laptops"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}]}}}, {"name": "Get Laptop by Slug", "request": {"method": "GET", "header": [], "url": "{{base_url}}/api/laptops/by-slug/{{laptop_slug}}"}}]}, {"name": "📂 Categories (Public)", "item": [{"name": "Get Categories", "request": {"method": "GET", "header": [], "url": "{{base_url}}/api/categories"}}]}, {"name": "🔧 Redis Testing", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": "{{base_url}}/api/redis/ping"}}, {"name": "List Redis Keys", "request": {"method": "GET", "header": [], "url": "{{base_url}}/api/redis/keys"}}]}, {"name": "🎮 GraphQL", "item": [{"name": "GraphQL Playground", "request": {"method": "GET", "header": [], "url": "{{base_url}}/graphql"}}, {"name": "Get Categories (GraphQL)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query GetCategories($page: Int, $size: Int) {\\n  categories(page: $page, size: $size) {\\n    id\\n    name\\n    description\\n    categoryType\\n    isActive\\n  }\\n}\",\n  \"variables\": {\n    \"page\": 1,\n    \"size\": 5\n  }\n}"}, "url": "{{base_url}}/graphql"}}]}, {"name": "🖼️ Image Management", "item": [{"name": "Cloudinary Health Check", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": "{{base_url}}/api/image/health"}}]}, {"name": "📊 Progression System", "item": [{"name": "Get Leaderboard", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/leaderboard?page=1&size=10", "host": ["{{base_url}}"], "path": ["api", "leaderboard"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}]}}}]}]}