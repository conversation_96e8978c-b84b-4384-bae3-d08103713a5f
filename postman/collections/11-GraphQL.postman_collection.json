{"info": {"name": "11-GraphQL", "description": "GraphQL API endpoints for querying data", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "GraphQL Playground", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/graphql", "host": ["{{base_url}}"], "path": ["graphql"]}, "description": "Access GraphQL Playground interface for interactive queries"}}, {"name": "Get Categories (GraphQL)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query GetCategories($page: Int, $size: Int) {\\n  categories(page: $page, size: $size) {\\n    id\\n    name\\n    description\\n    categoryType\\n    isActive\\n    createdAt\\n    updatedAt\\n  }\\n}\",\n  \"variables\": {\n    \"page\": 1,\n    \"size\": 10\n  }\n}"}, "url": {"raw": "{{base_url}}/graphql", "host": ["{{base_url}}"], "path": ["graphql"]}, "description": "Query categories using GraphQL with pagination"}}, {"name": "Get Laptops (GraphQL)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query GetLaptops($page: Int, $size: Int) {\\n  laptops(page: $page, size: $size) {\\n    id\\n    name\\n    slug\\n    description\\n    brand\\n    model\\n    isActive\\n    isFeatured\\n    viewCount\\n    createdAt\\n    updatedAt\\n  }\\n}\",\n  \"variables\": {\n    \"page\": 1,\n    \"size\": 10\n  }\n}"}, "url": {"raw": "{{base_url}}/graphql", "host": ["{{base_url}}"], "path": ["graphql"]}, "description": "Query laptops using GraphQL with pagination"}}, {"name": "Complex Query - Categories with Laptops", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query GetCategoriesAndLaptops {\\n  categories(page: 1, size: 5) {\\n    id\\n    name\\n    categoryType\\n    isActive\\n  }\\n  laptops(page: 1, size: 5) {\\n    id\\n    name\\n    brand\\n    model\\n    isFeatured\\n    viewCount\\n  }\\n}\"\n}"}, "url": {"raw": "{{base_url}}/graphql", "host": ["{{base_url}}"], "path": ["graphql"]}, "description": "Complex GraphQL query to get both categories and laptops in a single request"}}, {"name": "GraphQL Introspection Query", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query IntrospectionQuery {\\n  __schema {\\n    queryType {\\n      name\\n      fields {\\n        name\\n        description\\n        type {\\n          name\\n          kind\\n        }\\n      }\\n    }\\n  }\\n}\"\n}"}, "url": {"raw": "{{base_url}}/graphql", "host": ["{{base_url}}"], "path": ["graphql"]}, "description": "Introspection query to discover available GraphQL schema and operations"}}]}