{"info": {"name": "12-Profile", "description": "User profile management endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "item": [{"name": "Get My Profile", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private", "description": "Required for private API access"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "description": "JWT access token"}], "url": {"raw": "{{base_url}}/api/profile/me", "host": ["{{base_url}}"], "path": ["api", "profile", "me"]}, "description": "Get current authenticated user's profile including user data, progression info, and current title"}}]}