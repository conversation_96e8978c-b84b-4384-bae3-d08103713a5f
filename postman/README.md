# 🚀 Platform Rust API - Postman Collections

Thư mục này chứa tất cả Postman collections và environments để test Platform Rust API.

## 📦 Collections Available

### Core Collections
- **01-Authentication** - Authentication endpoints (login, register, OAuth, refresh token)
- **02-Image-Updated** - Image management endpoints (Cloudinary integration)
- **03-Laptop-Updated** - Laptop management endpoints (CRUD operations)
- **04-Category-Updated** - Category management endpoints
- **05-User-Updated** - User management endpoints
- **06-Role-Updated** - Role management endpoints
- **07-Permission-Updated** - Permission management endpoints
- **08-Progression-Updated** - Progression system endpoints (EXP, levels, leaderboard)

### New Collections (Recently Added)
- **09-Redis** - Redis operations and testing endpoints
- **10-Health** - Health check endpoints
- **11-GraphQL** - GraphQL queries and playground access
- **12-Profile** - User profile management
- **13-API-Testing-Suite** - Comprehensive testing suite with all major endpoints

## 🌍 Environment Setup

Import the environment file:
- **Platform-Rust-API.postman_environment.json** - Contains all necessary variables

### Environment Variables
```json
{
  "base_url": "http://localhost:8386",
  "test_email": "<EMAIL>",
  "test_password": "SuperAdmin123!",
  "access_token": "", // Auto-populated after login
  "refresh_token": "", // Auto-populated after login
  "redis_key": "test_key",
  "oauth_code": "",
  "oauth_state": "",
  "laptop_slug": "apple-macbook-pro-14-m3-pro",
  "laptop_id": "",
  "category_id": "",
  "user_id": "",
  "role_id": "",
  "permission_id": ""
}
```

## 🔧 Quick Start

1. **Import Collections**: Import all `.postman_collection.json` files
2. **Import Environment**: Import `Platform-Rust-API.postman_environment.json`
3. **Start Server**: Make sure your Rust server is running on `http://localhost:8386`
4. **Login**: Run the Login request in Authentication collection to get access token
5. **Test APIs**: Use any collection to test different endpoints

## 📋 Testing Workflow

### Recommended Testing Order:
1. **Health Checks** (10-Health) - Verify server is running
2. **Authentication** (01-Authentication) - Login and get tokens
3. **Profile** (12-Profile) - Test authenticated user profile
4. **Public APIs** - Categories, Laptops (public endpoints)
5. **Private APIs** - User, Role, Permission management
6. **GraphQL** (11-GraphQL) - Test GraphQL queries
7. **Redis** (09-Redis) - Test Redis operations
8. **Comprehensive Suite** (13-API-Testing-Suite) - Run full test suite

## 🎯 Special Features

### GraphQL Collection (11-GraphQL)
- GraphQL Playground access
- Sample queries for categories and laptops
- Complex queries combining multiple resources
- Introspection queries for schema discovery

### Redis Collection (09-Redis)
- Redis connectivity testing
- Key-value operations
- Queue monitoring
- Performance testing

### API Testing Suite (13-API-Testing-Suite)
- One-stop collection for testing all major endpoints
- Organized by feature areas
- Includes both public and private API tests
- Perfect for CI/CD integration

## 🔐 Authentication Notes

- Most private endpoints require `X-API-Type: private` header
- JWT tokens are automatically managed through test scripts
- OAuth endpoints support Google and GitHub providers
- Refresh token functionality is included

## 🚨 Troubleshooting

### Common Issues:
1. **401 Unauthorized**: Make sure you're logged in and access_token is set
2. **403 Forbidden**: Check if you have required permissions for the endpoint
3. **404 Not Found**: Verify the server is running and endpoint paths are correct
4. **500 Internal Error**: Check server logs for detailed error information

### Debug Tips:
- Use Health Check endpoints to verify server status
- Check Redis connectivity if caching issues occur
- Use GraphQL Playground for interactive query testing
- Monitor server logs for detailed error information

## 📚 Related Documentation

- [API Documentation](../docs/API.md)
- [Architecture Guide](../docs/ARCHITECTURE.md)
- [Main README](../README.md)
- [Swagger UI](http://127.0.0.1:8386/swagger-ui) (when server is running)
