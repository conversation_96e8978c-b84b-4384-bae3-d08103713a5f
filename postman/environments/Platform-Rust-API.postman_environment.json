{"id": "platform-rust-environment", "name": "Platform Rust API - Environment", "values": [{"key": "base_url", "value": "http://localhost:8386", "description": "Base URL for the Platform Rust API", "enabled": true}, {"key": "test_email", "value": "<EMAIL>", "description": "Test email for authentication", "enabled": true}, {"key": "test_password", "value": "SuperAdmin123!", "description": "Test password for authentication", "enabled": true}, {"key": "access_token", "value": "", "description": "JWT access token (auto-populated)", "enabled": true}, {"key": "refresh_token", "value": "", "description": "JWT refresh token (auto-populated)", "enabled": true}, {"key": "redis_key", "value": "test_key", "description": "Redis key for testing", "enabled": true}, {"key": "oauth_code", "value": "", "description": "OAuth authorization code from provider", "enabled": true}, {"key": "oauth_state", "value": "", "description": "OAuth state parameter for CSRF protection", "enabled": true}, {"key": "laptop_slug", "value": "apple-macbook-pro-14-m3-pro", "description": "Sample laptop slug for testing", "enabled": true}, {"key": "laptop_id", "value": "", "description": "Laptop ID for testing (auto-populated)", "enabled": true}, {"key": "category_id", "value": "", "description": "Category ID for testing (auto-populated)", "enabled": true}, {"key": "user_id", "value": "", "description": "User ID for testing (auto-populated)", "enabled": true}, {"key": "role_id", "value": "", "description": "Role ID for testing (auto-populated)", "enabled": true}, {"key": "permission_id", "value": "", "description": "Permission ID for testing (auto-populated)", "enabled": true}, {"key": "user_id", "value": "", "description": "Current user ID (auto-populated)", "enabled": true}, {"key": "test_user_email", "value": "<EMAIL>", "description": "Test user email for user management", "enabled": true}, {"key": "test_role_id", "value": "", "description": "Test role ID (auto-populated)", "enabled": true}, {"key": "test_permission_id", "value": "", "description": "Test permission ID (auto-populated)", "enabled": true}, {"key": "test_laptop_id", "value": "", "description": "Test laptop ID (auto-populated)", "enabled": true}, {"key": "test_category_id", "value": "", "description": "Test category ID (auto-populated)", "enabled": true}], "_postman_variable_scope": "environment"}