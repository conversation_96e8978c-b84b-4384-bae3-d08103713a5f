use crate::{
    constants::{API_LAPTOPS_PATH, API_PRICES_PATH, API_SPECIFICATIONS_PATH},
    errors::{AppError, Result},
    modules::laptop::{
        models::{
            CompleteLaptopView, CreateCompleteLaptopRequest, CreatePriceRequest,
            CreateSpecificationRequest, LaptopFullView, LaptopPaginationRequest, Price,
            Specification, UpdateLaptopRequest, UpdatePriceRequest, UpdateSpecificationRequest,
        },
        service_trait::LaptopManagementServiceTrait,
    },
    response::error_response,
    routes::middleware::{ApiType, auth::AuthenticatedUser},
    utils::response_helpers::{
        ResponseHelper,
        response_constants::{laptop, price, specification},
    },
};
use axum::{
    Extension, Json,
    extract::{Path, Query, State},
    response::{IntoResponse, Response},
};
use serde_json;
use std::sync::Arc;
use utoipa::OpenApi;
use utoipa::ToSchema;
use uuid::Uuid;

#[derive(OpenApi)]
#[openapi(
    paths(
        get_laptops, get_laptop_by_slug, increment_laptop_view_count,
        create_complete_laptop, get_laptop_by_id, update_laptop, delete_laptop,
        publish_laptop, archive_laptop, set_laptop_featured,
        create_specification, get_specification_by_id, update_specification, delete_specification, get_specifications_by_laptop_id,
        create_price, get_price_by_id, update_price, delete_price, get_prices_by_laptop_id, set_current_price
    ),
    components(schemas(
        CompleteLaptopView, CreateCompleteLaptopRequest, CreatePriceRequest,
        CreateSpecificationRequest, LaptopFullView, LaptopPaginationRequest, Price,
        Specification, UpdateLaptopRequest, UpdatePriceRequest, UpdateSpecificationRequest,
        SetFeaturedRequest, GetPricesQuery
    )),
    tags((name = "Laptops", description = "Laptop management endpoints"))
)]
pub struct LaptopApiDoc;

// ===== LAPTOP HANDLERS (Support both Public and Private API via header) =====

/// Get laptops with pagination
/// - Public API (no header or X-API-Type: public): Returns only published laptops with limited fields
/// - Private API (X-API-Type: private + auth): Returns all laptops with detailed/full fields based on permissions
#[utoipa::path(
    get,
    path = "/api/laptops",
    tag = "Laptops",
    params(
        ("page" = Option<i64>, Query, description = "Page number"),
        ("per_page" = Option<i64>, Query, description = "Items per page"),
        ("search" = Option<String>, Query, description = "Search term"),
        ("brand" = Option<String>, Query, description = "Filter by brand"),
        ("category_id" = Option<String>, Query, description = "Filter by category ID"),
        ("status" = Option<String>, Query, description = "Filter by status (private API only)"),
        ("is_featured" = Option<bool>, Query, description = "Filter by featured status"),
        ("market_region" = Option<String>, Query, description = "Filter by market region")
    ),
    responses(
        (status = 200, description = "Laptops retrieved successfully"),
        (status = 400, description = "Invalid query parameters")
    )
)]
pub async fn get_laptops(
    Extension(api_type): Extension<ApiType>,
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Query(pagination): Query<LaptopPaginationRequest>,
    user: Option<Extension<AuthenticatedUser>>,
) -> Result<Response> {
    match api_type {
        ApiType::Public => {
            // Public API: only published laptops with enhanced details (spec + price)
            let laptops = laptop_service
                .get_public_laptops_with_details(pagination)
                .await?;

            let response = crate::response::success_response(
                API_LAPTOPS_PATH.to_string(),
                laptop::SUCCESS_LIST,
                laptop::MSG_LISTED,
                laptops,
            );
            Ok(response.into_response())
        }
        ApiType::Private => {
            // Private API: requires authentication
            if user.is_none() {
                return Err(AppError::Unauthorized(
                    "Authentication required for private API access".into(),
                ));
            }

            let authenticated_user = match user {
                Some(Extension(user)) => user,
                None => {
                    return Err(AppError::Unauthorized(
                        "Authentication required for private API access".into(),
                    ));
                }
            };

            // Check if user has admin permissions for full view
            let has_admin_permission = authenticated_user
                .permissions
                .iter()
                .any(|p| p == "admin:all" || p == "laptops:read");

            if has_admin_permission {
                // Admin users get full view with audit fields and enhanced details
                let laptops = laptop_service
                    .get_full_laptops_with_details(pagination)
                    .await?;

                let response = crate::response::success_response(
                    API_LAPTOPS_PATH.to_string(),
                    laptop::SUCCESS_LIST,
                    laptop::MSG_LISTED,
                    laptops,
                );
                Ok(response.into_response())
            } else {
                // Regular authenticated users get public detailed view with enhanced data
                let laptops = laptop_service
                    .get_public_laptops_with_details(pagination)
                    .await?;

                let response = crate::response::success_response(
                    API_LAPTOPS_PATH.to_string(),
                    laptop::SUCCESS_LIST,
                    laptop::MSG_LISTED,
                    laptops,
                );
                Ok(response.into_response())
            }
        }
    }
}

/// Get laptop by slug
/// - Public API: Returns only if published with limited fields
/// - Private API: Returns any status with detailed/full fields based on permissions
#[utoipa::path(
    get,
    path = "/api/laptops/{slug}",
    tag = "Laptops",
    params(
        ("slug" = String, Path, description = "Laptop slug")
    ),
    responses(
        (status = 200, description = "Laptop retrieved successfully"),
        (status = 404, description = "Laptop not found")
    )
)]
pub async fn get_laptop_by_slug(
    Extension(api_type): Extension<ApiType>,
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(slug): Path<String>,
    user: Option<Extension<AuthenticatedUser>>,
) -> Result<Response> {
    match api_type {
        ApiType::Public => {
            // Public API: only published laptops with enhanced details
            match laptop_service
                .get_public_laptop_by_slug_with_details(&slug)
                .await
            {
                Ok(laptop) => {
                    let path = format!("{API_LAPTOPS_PATH}/by-slug/{slug}");
                    let response = crate::response::success_response(
                        path,
                        laptop::SUCCESS_GET,
                        laptop::MSG_RETRIEVED,
                        laptop,
                    );
                    Ok(response.into_response())
                }
                Err(AppError::NotFound(error_msg)) => {
                    let path = format!("{API_LAPTOPS_PATH}/by-slug/{slug}");
                    let response = error_response(
                        path,
                        axum::http::StatusCode::NOT_FOUND,
                        "SYS06",
                        &error_msg,
                    );
                    Ok(response.into_response())
                }
                Err(e) => Err(e),
            }
        }
        ApiType::Private => {
            // Private API: requires authentication
            if user.is_none() {
                return Err(AppError::Unauthorized(
                    "Authentication required for private API access".into(),
                ));
            }

            let authenticated_user = match user {
                Some(Extension(user)) => user,
                None => {
                    return Err(AppError::Unauthorized(
                        "Authentication required for private API access".into(),
                    ));
                }
            };

            // Check if user has admin permissions for full view
            let has_admin_permission = authenticated_user
                .permissions
                .iter()
                .any(|p| p == "admin:all" || p == "laptops:read");

            let path = format!("{API_LAPTOPS_PATH}/by-slug/{slug}");

            if has_admin_permission {
                // Admin users get full view with audit fields and enhanced details
                match laptop_service.get_laptop_by_slug_with_details(&slug).await {
                    Ok(laptop) => {
                        let response = crate::response::success_response(
                            path,
                            laptop::SUCCESS_GET,
                            laptop::MSG_RETRIEVED,
                            laptop,
                        );
                        Ok(response.into_response())
                    }
                    Err(AppError::NotFound(error_msg)) => {
                        let response = error_response(
                            path,
                            axum::http::StatusCode::NOT_FOUND,
                            "SYS06",
                            &error_msg,
                        );
                        Ok(response.into_response())
                    }
                    Err(e) => Err(e),
                }
            } else {
                // Regular authenticated users get public detailed view with enhanced data
                match laptop_service
                    .get_public_laptop_by_slug_with_details(&slug)
                    .await
                {
                    Ok(laptop) => {
                        let response = crate::response::success_response(
                            path,
                            laptop::SUCCESS_GET,
                            laptop::MSG_RETRIEVED,
                            laptop,
                        );
                        Ok(response.into_response())
                    }
                    Err(AppError::NotFound(error_msg)) => {
                        let response = error_response(
                            path,
                            axum::http::StatusCode::NOT_FOUND,
                            "SYS06",
                            &error_msg,
                        );
                        Ok(response.into_response())
                    }
                    Err(e) => Err(e),
                }
            }
        }
    }
}

/// Increment laptop view count (Public API only)
#[utoipa::path(
    post,
    path = "/api/laptops/{id}/view",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    responses(
        (status = 200, description = "View count incremented successfully"),
        (status = 404, description = "Laptop not found")
    )
)]
pub async fn increment_laptop_view_count(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    laptop_service.increment_laptop_view_count(&id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&id.to_string()),
        laptop::SUCCESS_VIEW_COUNT,
        laptop::MSG_VIEW_COUNT,
        serde_json::json!({"id": id, "view_incremented": true}),
    ))
}

// ===== MANAGEMENT HANDLERS (Private API - Authentication + Permission Required) =====

/// Create complete laptop with specifications and price in one request (Private API only)
#[utoipa::path(
    post,
    path = "/api/laptops/complete",
    tag = "Laptops",
    request_body = CreateCompleteLaptopRequest,
    responses(
        (status = 201, description = "Complete laptop created successfully", body = CompleteLaptopView),
        (status = 400, description = "Invalid request data"),
        (status = 409, description = "Laptop already exists")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn create_complete_laptop(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Json(request): Json<CreateCompleteLaptopRequest>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    let complete_laptop = laptop_service
        .create_complete_laptop(request, &user_id)
        .await?;

    Ok(ResponseHelper::entity_created(
        API_LAPTOPS_PATH,
        laptop::SUCCESS_CREATE,
        "Complete laptop created successfully",
        complete_laptop,
    ))
}

/// Get laptop by ID (Private API only)
#[utoipa::path(
    get,
    path = "/api/laptops/{id}",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    responses(
        (status = 200, description = "Laptop retrieved successfully", body = LaptopFullView),
        (status = 404, description = "Laptop not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_laptop_by_id(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Path(id): Path<Uuid>,
) -> Result<Response> {
    // Check if user has admin permissions for full view
    let has_admin_permission = user
        .permissions
        .iter()
        .any(|p| p == "admin:all" || p == "laptops:read");

    let path = format!("{API_LAPTOPS_PATH}/{id}");

    if has_admin_permission {
        // Admin users get full view with audit fields and enhanced details
        let laptop = laptop_service.get_laptop_by_id_with_details(&id).await?;
        let response = crate::response::success_response(
            path,
            laptop::SUCCESS_GET,
            laptop::MSG_RETRIEVED,
            laptop,
        );
        Ok(response.into_response())
    } else {
        // Regular authenticated users get public detailed view with enhanced data
        let laptop = laptop_service
            .get_public_laptop_by_id_with_details(&id)
            .await?;
        let response = crate::response::success_response(
            path,
            laptop::SUCCESS_GET,
            laptop::MSG_RETRIEVED,
            laptop,
        );
        Ok(response.into_response())
    }
}

/// Update laptop (Private API only)
#[utoipa::path(
    put,
    path = "/api/laptops/{id}",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    request_body = UpdateLaptopRequest,
    responses(
        (status = 200, description = "Laptop updated successfully", body = LaptopFullView),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Laptop not found"),
        (status = 409, description = "Laptop slug/SKU conflict")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_laptop(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Path(id): Path<Uuid>,
    Json(request): Json<UpdateLaptopRequest>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    let laptop = laptop_service.update_laptop(&id, request, &user_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&id.to_string()),
        laptop::SUCCESS_UPDATE,
        laptop::MSG_UPDATED,
        laptop,
    ))
}

/// Delete laptop (Private API only)
#[utoipa::path(
    delete,
    path = "/api/laptops/{id}",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    responses(
        (status = 200, description = "Laptop deleted successfully"),
        (status = 404, description = "Laptop not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn delete_laptop(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    laptop_service.delete_laptop(&id).await?;

    Ok(ResponseHelper::entity_deleted(
        API_LAPTOPS_PATH,
        &id.to_string(),
        laptop::SUCCESS_DELETE,
        laptop::MSG_DELETED,
    ))
}

/// Publish laptop (Private API only)
#[utoipa::path(
    put,
    path = "/api/laptops/{id}/publish",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    responses(
        (status = 200, description = "Laptop published successfully"),
        (status = 404, description = "Laptop not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn publish_laptop(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    laptop_service.publish_laptop(&id, &user_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&id.to_string()),
        laptop::SUCCESS_PUBLISH,
        laptop::MSG_PUBLISHED,
        serde_json::json!({"id": id, "status": "published"}),
    ))
}

/// Archive laptop (Private API only)
#[utoipa::path(
    put,
    path = "/api/laptops/{id}/archive",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    responses(
        (status = 200, description = "Laptop archived successfully"),
        (status = 404, description = "Laptop not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn archive_laptop(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    laptop_service.archive_laptop(&id, &user_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&id.to_string()),
        laptop::SUCCESS_ARCHIVE,
        laptop::MSG_ARCHIVED,
        serde_json::json!({"id": id, "status": "archived"}),
    ))
}

/// Set laptop featured status (Private API only)
#[utoipa::path(
    put,
    path = "/api/laptops/{id}/featured",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    request_body = inline(SetFeaturedRequest),
    responses(
        (status = 200, description = "Laptop featured status updated successfully"),
        (status = 404, description = "Laptop not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn set_laptop_featured(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Path(id): Path<Uuid>,
    Json(request): Json<SetFeaturedRequest>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    laptop_service
        .set_laptop_featured(&id, request.is_featured, &user_id)
        .await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&id.to_string()),
        laptop::SUCCESS_FEATURE,
        laptop::MSG_FEATURED,
        serde_json::json!({"id": id, "is_featured": request.is_featured}),
    ))
}

// ===== SPECIFICATION HANDLERS =====

/// Create specification
#[utoipa::path(
    post,
    path = "/api/specifications",
    tag = "Specifications",
    request_body = CreateSpecificationRequest,
    responses(
        (status = 201, description = "Specification created successfully", body = Specification),
        (status = 400, description = "Invalid request data"),
        (status = 409, description = "Specification already exists for laptop")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn create_specification(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Json(request): Json<CreateSpecificationRequest>,
) -> Result<impl IntoResponse> {
    let specification = laptop_service.create_specification(request).await?;

    Ok(ResponseHelper::entity_created(
        API_SPECIFICATIONS_PATH,
        specification::SUCCESS_CREATE,
        specification::MSG_CREATED,
        specification,
    ))
}

/// Get specification by ID
#[utoipa::path(
    get,
    path = "/api/specifications/{id}",
    tag = "Specifications",
    params(
        ("id" = String, Path, description = "Specification ID")
    ),
    responses(
        (status = 200, description = "Specification retrieved successfully", body = Specification),
        (status = 404, description = "Specification not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_specification_by_id(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    let specification = laptop_service.get_specification_by_id(&id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_SPECIFICATIONS_PATH,
        Some(&id.to_string()),
        specification::SUCCESS_GET,
        specification::MSG_RETRIEVED,
        specification,
    ))
}

/// Update specification (Private API only)
#[utoipa::path(
    put,
    path = "/api/specifications/{id}",
    tag = "Specifications",
    params(
        ("id" = String, Path, description = "Specification ID")
    ),
    request_body = UpdateSpecificationRequest,
    responses(
        (status = 200, description = "Specification updated successfully", body = Specification),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Specification not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_specification(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
    Json(request): Json<UpdateSpecificationRequest>,
) -> Result<impl IntoResponse> {
    let specification = laptop_service.update_specification(&id, request).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_SPECIFICATIONS_PATH,
        Some(&id.to_string()),
        specification::SUCCESS_UPDATE,
        specification::MSG_UPDATED,
        specification,
    ))
}

/// Delete specification (Private API only)
#[utoipa::path(
    delete,
    path = "/api/specifications/{id}",
    tag = "Specifications",
    params(
        ("id" = String, Path, description = "Specification ID")
    ),
    responses(
        (status = 200, description = "Specification deleted successfully"),
        (status = 404, description = "Specification not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn delete_specification(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    laptop_service.delete_specification(&id).await?;

    Ok(ResponseHelper::entity_deleted(
        API_SPECIFICATIONS_PATH,
        &id.to_string(),
        specification::SUCCESS_DELETE,
        specification::MSG_DELETED,
    ))
}

/// Get specifications by laptop ID (Public API)
#[utoipa::path(
    get,
    path = "/api/laptops/{laptop_id}/specifications",
    tag = "Specifications",
    params(
        ("laptop_id" = String, Path, description = "Laptop ID")
    ),
    responses(
        (status = 200, description = "Specifications retrieved successfully", body = Option<Specification>),
        (status = 404, description = "Laptop not found")
    )
)]
pub async fn get_specifications_by_laptop_id(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(laptop_id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    let specification = laptop_service
        .get_specification_by_laptop_id(&laptop_id)
        .await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&format!("{laptop_id}/specifications")),
        specification::SUCCESS_GET,
        specification::MSG_RETRIEVED,
        specification,
    ))
}

// ===== PRICE HANDLERS =====

/// Create price (Private API only)
#[utoipa::path(
    post,
    path = "/api/prices",
    tag = "Prices",
    request_body = CreatePriceRequest,
    responses(
        (status = 201, description = "Price created successfully", body = Price),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Laptop not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn create_price(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Json(request): Json<CreatePriceRequest>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    let price = laptop_service.create_price(request, &user_id).await?;

    Ok(ResponseHelper::entity_created(
        API_PRICES_PATH,
        price::SUCCESS_CREATE,
        price::MSG_CREATED,
        price,
    ))
}

/// Get price by ID (Private API only)
#[utoipa::path(
    get,
    path = "/api/prices/{id}",
    tag = "Prices",
    params(
        ("id" = String, Path, description = "Price ID")
    ),
    responses(
        (status = 200, description = "Price retrieved successfully", body = Price),
        (status = 404, description = "Price not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_price_by_id(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    let price = laptop_service.get_price_by_id(&id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_PRICES_PATH,
        Some(&id.to_string()),
        price::SUCCESS_GET,
        price::MSG_RETRIEVED,
        price,
    ))
}

/// Update price (Private API only)
#[utoipa::path(
    put,
    path = "/api/prices/{id}",
    tag = "Prices",
    params(
        ("id" = String, Path, description = "Price ID")
    ),
    request_body = UpdatePriceRequest,
    responses(
        (status = 200, description = "Price updated successfully", body = Price),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Price not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_price(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Path(id): Path<Uuid>,
    Json(request): Json<UpdatePriceRequest>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    let price = laptop_service.update_price(&id, request, &user_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_PRICES_PATH,
        Some(&id.to_string()),
        price::SUCCESS_UPDATE,
        price::MSG_UPDATED,
        price,
    ))
}

/// Delete price (Private API only)
#[utoipa::path(
    delete,
    path = "/api/prices/{id}",
    tag = "Prices",
    params(
        ("id" = String, Path, description = "Price ID")
    ),
    responses(
        (status = 200, description = "Price deleted successfully"),
        (status = 404, description = "Price not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn delete_price(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    laptop_service.delete_price(&id).await?;

    Ok(ResponseHelper::entity_deleted(
        API_PRICES_PATH,
        &id.to_string(),
        price::SUCCESS_DELETE,
        price::MSG_DELETED,
    ))
}

/// Get prices by laptop ID (Public API)
#[utoipa::path(
    get,
    path = "/api/laptops/{laptop_id}/prices",
    tag = "Prices",
    params(
        ("laptop_id" = String, Path, description = "Laptop ID"),
        ("current_only" = Option<bool>, Query, description = "Get only current prices")
    ),
    responses(
        (status = 200, description = "Prices retrieved successfully", body = Vec<Price>),
        (status = 404, description = "Laptop not found")
    )
)]
pub async fn get_prices_by_laptop_id(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(laptop_id): Path<Uuid>,
    Query(params): Query<GetPricesQuery>,
) -> Result<impl IntoResponse> {
    let prices = if params.current_only.unwrap_or(false) {
        laptop_service
            .get_current_prices_by_laptop_id(&laptop_id)
            .await?
    } else {
        laptop_service.get_prices_by_laptop_id(&laptop_id).await?
    };

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&format!("{laptop_id}/prices")),
        price::SUCCESS_LIST,
        price::MSG_LISTED,
        prices,
    ))
}

/// Set current price for laptop (Private API only)
#[utoipa::path(
    put,
    path = "/api/laptops/{laptop_id}/prices/{price_id}/set-current",
    tag = "Prices",
    params(
        ("laptop_id" = String, Path, description = "Laptop ID"),
        ("price_id" = String, Path, description = "Price ID")
    ),
    responses(
        (status = 200, description = "Current price set successfully"),
        (status = 400, description = "Price does not belong to laptop"),
        (status = 404, description = "Laptop or price not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn set_current_price(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path((laptop_id, price_id)): Path<(Uuid, Uuid)>,
) -> Result<impl IntoResponse> {
    laptop_service
        .set_current_price(&laptop_id, &price_id)
        .await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&format!("{laptop_id}/prices/{price_id}/set-current")),
        price::SUCCESS_SET_CURRENT,
        price::MSG_SET_CURRENT,
        serde_json::json!({"laptop_id": laptop_id, "price_id": price_id, "set_as_current": true}),
    ))
}

// ===== REQUEST/RESPONSE MODELS =====

#[derive(serde::Deserialize, ToSchema)]
pub struct SetFeaturedRequest {
    pub is_featured: bool,
}

#[derive(serde::Deserialize, ToSchema)]
pub struct GetPricesQuery {
    pub current_only: Option<bool>,
}
